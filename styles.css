/* Font Imports */
@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #8fbc8f, #98d982);
    border-radius: 10px;
    border: 2px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #7ba87b, #85c975);
}

/* Header styles are now handled by header-and-footer.js */

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    padding-top: 0;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    min-height: 100vh;
}

.hero-text {
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px;
    position: relative;
}

.main-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    letter-spacing: 3px;
    line-height: 1.1;
}

.subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.circle {
    color: #8fbc8f;
    font-size: 0.8em;
    font-weight: bold;
}

.hero-image {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 5px;
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 3px;
    background: #8fbc8f;
    border-radius: 2px;
    opacity: 0.7;
}

.scroll-arrow:nth-child(1) {
    animation-delay: 0s;
}

.scroll-arrow:nth-child(2) {
    animation-delay: 0.2s;
}

.scroll-arrow:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
        opacity: 0.7;
    }
    40% {
        transform: translateY(-10px);
        opacity: 1;
    }
    60% {
        transform: translateY(-5px);
        opacity: 0.9;
    }
}

/* Placeholder Section */
.placeholder-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.placeholder-section p {
    font-size: 1.5rem;
    color: #6c757d;
    font-weight: 300;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .hero-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
    }
    
    .hero-text {
        padding: 40px 30px;
        text-align: center;
        align-items: center;
    }
    
    .main-title {
        font-size: clamp(2rem, 8vw, 3rem);
        margin-bottom: 15px;
    }
    
    .subtitle {
        font-size: clamp(1rem, 4vw, 1.4rem);
        justify-content: center;
        text-align: center;
    }
    
    .hero-image {
        min-height: 50vh;
    }
    
    .scroll-indicator {
        bottom: 20px;
    }
}

@media (max-width: 480px) {
    .hero-text {
        padding: 30px 20px;
    }

    .subtitle {
        gap: 10px;
    }
}

/* Impressum Page Styles */
.impressum-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
    padding-top: 80px;
}

.impressum-content {
    max-width: 800px;
    margin: 0 auto;
    padding: 40px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-top: 40px;
    margin-bottom: 40px;
}

.impressum-content h2 {
    color: #2c3e50;
    font-family: 'Montserrat', sans-serif;
    font-weight: 700;
    font-size: 2rem;
    margin-bottom: 30px;
    margin-top: 40px;
}

.impressum-content h2:first-child {
    margin-top: 0;
}

.impressum-content h4 {
    color: #2c3e50;
    font-family: 'Montserrat', sans-serif;
    font-weight: 600;
    font-size: 1.2rem;
    margin-bottom: 15px;
    margin-top: 30px;
}

.impressum-content p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 20px;
    font-size: 1rem;
}

.contact-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    border-left: 4px solid #8fbc8f;
    margin-bottom: 20px;
}

.contact-info p {
    margin: 0;
    color: #2c3e50;
    font-weight: 500;
}

@media (max-width: 768px) {
    .impressum-page {
        padding-top: 60px;
    }

    .impressum-content {
        margin: 20px;
        padding: 30px 20px;
    }

    .impressum-content h2 {
        font-size: 1.5rem;
    }

    .impressum-content h4 {
        font-size: 1.1rem;
    }
}
