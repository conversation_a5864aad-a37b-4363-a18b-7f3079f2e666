/* Font Imports */
@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-regular.woff2') format('woff2');
    font-weight: 400;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-600.woff2') format('woff2');
    font-weight: 600;
    font-style: normal;
}

@font-face {
    font-family: 'Montserrat';
    src: url('fonts/montserrat-v29-latin-700.woff2') format('woff2');
    font-weight: 700;
    font-style: normal;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Montserrat', sans-serif;
    line-height: 1.6;
    color: #2c3e50;
    overflow-x: hidden;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 12px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #8fbc8f, #98d982);
    border-radius: 10px;
    border: 2px solid #f1f1f1;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #7ba87b, #85c975);
}

/* Header */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    padding: 20px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.logo {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    letter-spacing: 2px;
}

/* Hamburger Menu */
.hamburger-menu {
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 4px;
    transition: all 0.3s ease;
}

.hamburger-menu span {
    width: 25px;
    height: 3px;
    background: #2c3e50;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.hamburger-menu:hover span {
    background: #8fbc8f;
}

/* Hero Section */
.hero {
    min-height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    padding-top: 80px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    min-height: calc(100vh - 80px);
}

.hero-text {
    background: #ffffff;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    padding: 60px;
    position: relative;
}

.main-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 20px;
    letter-spacing: 3px;
    line-height: 1.1;
}

.subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.8rem);
    font-weight: 400;
    color: #7f8c8d;
    letter-spacing: 1px;
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
}

.circle {
    color: #8fbc8f;
    font-size: 0.8em;
    font-weight: bold;
}

.hero-image {
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
}

/* Scroll Indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 5px;
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 20px;
    height: 3px;
    background: #8fbc8f;
    border-radius: 2px;
    opacity: 0.7;
}

.scroll-arrow:nth-child(1) {
    animation-delay: 0s;
}

.scroll-arrow:nth-child(2) {
    animation-delay: 0.2s;
}

.scroll-arrow:nth-child(3) {
    animation-delay: 0.4s;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
        opacity: 0.7;
    }
    40% {
        transform: translateY(-10px);
        opacity: 1;
    }
    60% {
        transform: translateY(-5px);
        opacity: 0.9;
    }
}

/* Placeholder Section */
.placeholder-section {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    text-align: center;
}

.placeholder-section p {
    font-size: 1.5rem;
    color: #6c757d;
    font-weight: 300;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .header {
        padding: 15px 20px;
    }
    
    .logo {
        font-size: 16px;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto;
    }
    
    .hero-text {
        padding: 40px 30px;
        text-align: center;
        align-items: center;
    }
    
    .main-title {
        font-size: clamp(2rem, 8vw, 3rem);
        margin-bottom: 15px;
    }
    
    .subtitle {
        font-size: clamp(1rem, 4vw, 1.4rem);
        justify-content: center;
        text-align: center;
    }
    
    .hero-image {
        min-height: 50vh;
    }
    
    .scroll-indicator {
        bottom: 20px;
    }
}

@media (max-width: 480px) {
    .hero-text {
        padding: 30px 20px;
    }
    
    .subtitle {
        gap: 10px;
    }
}
